package pages

import (
	"server-panel/internal/projects"
	"server-panel/internal/templates/layouts"
	"server-panel/internal/templates/components"
	"server-panel/internal/i18n"
	"strconv"
	"fmt"
)

func formatBytesSimple(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

templ Projects(title, username, currentLang string, overview *projects.ProjectOverview) {
	@layouts.Base(title, username, currentLang) {
		<div class="min-h-screen bg-gray-50 dark:bg-gray-900" x-data="projectManager()">
			<!-- Welcome Banner for First Time Setup -->
			if overview.FirstTimeSetup {
				@components.NotificationBanner(
					"welcome",
					i18n.T(currentLang, "projects.welcome.title"),
					i18n.T(currentLang, "projects.welcome.message"),
					[]components.BannerAction{
						{Text: i18n.T(currentLang, "projects.create.first"), OnClick: "showCreateModal = true", Style: "primary"},
						{Text: i18n.T(currentLang, "projects.skip.now"), OnClick: "", Style: "secondary"},
					},
					true,
				)
			}

			<!-- Header -->
			<div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="flex justify-between items-center py-6">
						<div>
							<h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">{ i18n.T(currentLang, "projects.title") }</h1>
							<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{ i18n.T(currentLang, "projects.subtitle") }</p>
						</div>
						<div class="flex space-x-3">
							<button
								@click="showCreateModal = true"
								class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
								<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
								</svg>
								{ i18n.T(currentLang, "projects.new_project") }
							</button>
							<button
								@click="refreshProjects()"
								class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
								<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
								</svg>
								{ i18n.T(currentLang, "projects.refresh") }
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Create Project Modal -->
			<div x-show="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-75 overflow-y-auto h-full w-full z-50">
				<div class="relative top-20 mx-auto p-5 border border-gray-200 dark:border-gray-700 w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
					<div class="mt-3">
						<h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">{ i18n.T(currentLang, "projects.create_title") }</h3>
						<form @submit.prevent="createProject()">
							<div class="mb-4">
								<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{ i18n.T(currentLang, "projects.project_name") }</label>
								<input
									type="text"
									x-model="newProject.name"
									class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
									placeholder="my-awesome-project"
									required>
							</div>
							<div class="mb-4">
								<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{ i18n.T(currentLang, "projects.domain") }</label>
								<input
									type="text"
									x-model="newProject.domain"
									class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
									placeholder="example.com">
							</div>
							<div class="mb-4 space-y-2">
								<label class="flex items-center">
									<input type="checkbox" x-model="newProject.createDB" class="mr-2 text-blue-600 dark:text-blue-400">
									<span class="text-sm text-gray-700 dark:text-gray-300">{ i18n.T(currentLang, "projects.create_database") }</span>
								</label>
								<label class="flex items-center">
									<input type="checkbox" x-model="newProject.enableSSL" class="mr-2 text-blue-600 dark:text-blue-400">
									<span class="text-sm text-gray-700 dark:text-gray-300">{ i18n.T(currentLang, "projects.enable_ssl") }</span>
								</label>
								<label class="flex items-center">
									<input type="checkbox" x-model="newProject.enableBackup" class="mr-2 text-blue-600 dark:text-blue-400">
									<span class="text-sm text-gray-700 dark:text-gray-300">{ i18n.T(currentLang, "projects.enable_backup") }</span>
								</label>
							</div>
							<div class="flex space-x-3">
								<button
									type="submit"
									class="flex-1 bg-blue-500 dark:bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300">
									{ i18n.T(currentLang, "projects.create_project") }
								</button>
								<button
									type="button"
									@click="showCreateModal = false"
									class="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md hover:bg-gray-400 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300">
									{ i18n.T(currentLang, "common.cancel") }
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>

			<!-- Main Content -->
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<!-- Stats Cards -->
				<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
					<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
						<div class="flex items-center">
							<div class="flex-shrink-0">
								<div class="w-8 h-8 bg-blue-500 dark:bg-blue-600 rounded-md flex items-center justify-center">
									<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
										<path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
									</svg>
								</div>
							</div>
							<div class="ml-5 w-0 flex-1">
								<dl>
									<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{ i18n.T(currentLang, "projects.total") }</dt>
									<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">{ strconv.Itoa(overview.TotalProjects) }</dd>
								</dl>
							</div>
						</div>
					</div>

					<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
						<div class="flex items-center">
							<div class="flex-shrink-0">
								<div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
									<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
									</svg>
								</div>
							</div>
							<div class="ml-5 w-0 flex-1">
								<dl>
									<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{ i18n.T(currentLang, "projects.active") }</dt>
									<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">{ strconv.Itoa(overview.ActiveProjects) }</dd>
								</dl>
							</div>
						</div>
					</div>

					<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
						<div class="flex items-center">
							<div class="flex-shrink-0">
								<div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
									<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
										<path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
									</svg>
								</div>
							</div>
							<div class="ml-5 w-0 flex-1">
								<dl>
									<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{ i18n.T(currentLang, "projects.total_size") }</dt>
									<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">{ formatBytesSimple(overview.TotalSize) }</dd>
								</dl>
							</div>
						</div>
					</div>

					<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
						<div class="flex items-center">
							<div class="flex-shrink-0">
								<div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
									<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1zM3 7a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1V7zM3 12a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z" clip-rule="evenodd"></path>
									</svg>
								</div>
							</div>
							<div class="ml-5 w-0 flex-1">
								<dl>
									<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{ i18n.T(currentLang, "projects.backups") }</dt>
									<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">0</dd>
								</dl>
							</div>
						</div>
					</div>
				</div>

				<!-- Project Cards -->
				if len(overview.Projects) > 0 {
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						for _, project := range overview.Projects {
							@components.ProjectCard(project)
						}
					</div>
				} else {
					<div class="text-center py-12">
						<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
						</svg>
						<h3 class="mt-2 text-sm font-medium text-gray-900">{ i18n.T(currentLang, "projects.no_projects") }</h3>
						<p class="mt-1 text-sm text-gray-500">{ i18n.T(currentLang, "projects.get_started") }</p>
						<div class="mt-6">
							<button
								@click="showCreateModal = true"
								class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
								<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
								</svg>
								{ i18n.T(currentLang, "projects.new_project") }
							</button>
						</div>
					</div>
				}
			</div>
		</div>

		<!-- Alpine.js Project Manager -->
		<script>
			function projectManager() {
				return {
					showCreateModal: false,
					newProject: {
						name: '',
						domain: '',
						createDB: true,
						enableSSL: false,
						enableBackup: false
					},

					refreshProjects() {
						window.location.reload();
					},

					createProject() {
						if (!this.newProject.name) {
							alert('Project name is required');
							return;
						}

						fetch('/api/projects/create', {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify(this.newProject)
						})
						.then(response => response.json())
						.then(data => {
							if (data.success) {
								this.showCreateModal = false;
								this.refreshProjects();
							} else {
								alert('Failed to create project: ' + data.error);
							}
						})
						.catch(error => {
							alert('Failed to create project: ' + error.message);
						});
					}
				}
			}
		</script>
	}
}
