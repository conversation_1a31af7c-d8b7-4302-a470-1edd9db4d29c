root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = ["-port", "9090", "-host", "0.0.0.0"]
  bin = "./tmp/digwis-panel"
  cmd = "go build -ldflags='-s -w' -o ./tmp/digwis-panel ."
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "node_modules", "data", "releases"]
  exclude_file = []
  exclude_regex = ["_test.go", "_templ.go", "\\.db$", "\\.sqlite$"]
  exclude_unchanged = true
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "templ"]
  include_file = []
  kill_delay = "2s"
  log = "build-errors.log"
  poll = false
  poll_interval = 0
  post_cmd = []
  pre_cmd = ["templ generate", "npm run build-css-prod"]
  rerun = false
  rerun_delay = 500
  send_interrupt = true
  stop_on_root = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  main_only = false
  time = false

[misc]
  clean_on_exit = false

[screen]
  clear_on_rebuild = false
  keep_scroll = true
